
!pip install datasets torch transformers

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset
import collections
import json
import os
import time
import math
import re
import pickle
from datasets import load_dataset
import numpy as np

print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"GPU: {torch.cuda.get_device_name(0)}")
    device = torch.device('cuda')
else:
    device = torch.device('cpu')
print(f"Using device: {device}")


class BPETokenizer:
    """
    Improved BPE tokenizer that fixes repetitive text and unknown token issues.
    """
    def __init__(self):
        self.vocab = {}
        self.merges = {}
        self.inverse_vocab = {}
        
        self.pad_token = '<pad>'
        self.unk_token = '<unk>'
        self.bos_token = '<bos>'
        self.eos_token = '<eos>'
        self.special_tokens = [self.pad_token, self.unk_token, self.bos_token, self.eos_token]

    def clean_text(self, text):
        """Clean and normalize text for better tokenization."""
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
        text = text.replace('"', '"').replace('"', '"')
        text = text.replace(''', "'").replace(''', "'")
        return text.strip()
    
    def is_repetitive_text(self, text):
        """Check if text contains repetitive patterns that should be filtered."""
        repetitive_patterns = [
            "to help language model learn patterns",
            "diverse collection",
            "model will learn",
            "patterns from this",
            "help train our",
            "language model",
            "training data"
        ]
        
        text_lower = text.lower()
        for pattern in repetitive_patterns:
            if pattern in text_lower:
                return True
        
        words = text.split()
        if len(words) > 5:
            for i in range(len(words) - 2):
                phrase = ' '.join(words[i:i+3])
                if text.count(phrase) > 2:
                    return True
        return False

    def get_pairs(self, tokens):
        pairs = collections.defaultdict(int)
        for i in range(len(tokens) - 1):
            pairs[(tokens[i], tokens[i+1])] += 1
        return pairs

    def train(self, texts, vocab_size=8000):
        print(f"Training improved tokenizer with vocab_size={vocab_size}")
 
        cleaned_texts = []
        filtered_count = 0
        for text in texts:
            cleaned = self.clean_text(text)
            if len(cleaned) > 20 and not self.is_repetitive_text(cleaned):
                cleaned_texts.append(cleaned)
            else:
                filtered_count += 1
        
        print(f"Kept {len(cleaned_texts)} texts, filtered out {filtered_count} repetitive/short texts")

        self.vocab = {token: i for i, token in enumerate(self.special_tokens)}
        self.inverse_vocab = {i: token for i, token in enumerate(self.special_tokens)}
        current_id = len(self.special_tokens)
        
        all_chars = set()
        for text in cleaned_texts:
            for char in text:
                all_chars.add(char)

        all_chars.add('</w>')

        for char in sorted(all_chars):
            if char not in self.vocab:
                self.vocab[char] = current_id
                self.inverse_vocab[current_id] = char
                current_id += 1

        word_freq = collections.defaultdict(int)
        for text in cleaned_texts:
            words = text.split()
            for word in words:
                if word.strip():
                    word_with_end = word + '</w>'
                    char_list = list(word_with_end)
                    word_freq[tuple(char_list)] += 1

        print(f"Built initial vocabulary with {len(self.vocab)} tokens")
        print(f"Processing {len(word_freq)} unique words")

        self.merges = {}
        while len(self.vocab) < vocab_size:
            pairs_count = collections.defaultdict(int)
            
            for word_chars, freq in word_freq.items():
                pairs = self.get_pairs(list(word_chars))
                for pair, count in pairs.items():
                    pairs_count[pair] += count * freq
            
            if not pairs_count:
                break
            
            best_pair = max(pairs_count.items(), key=lambda x: x[1])[0]
            new_token = best_pair[0] + best_pair[1]
            
            if new_token not in self.vocab:
                self.vocab[new_token] = current_id
                self.inverse_vocab[current_id] = new_token
                current_id += 1
            
            self.merges[best_pair] = new_token
            
            new_word_freq = collections.defaultdict(int)
            for word_chars, freq in word_freq.items():
                new_word = self._apply_merge(list(word_chars), best_pair, new_token)
                new_word_freq[tuple(new_word)] += freq
            
            word_freq = new_word_freq
            
            if len(self.vocab) % 1000 == 0:
                print(f"Vocabulary size: {len(self.vocab)}")

        print(f"Training complete! Final vocabulary size: {len(self.vocab)}")
        print(f"Number of merge rules: {len(self.merges)}")
    
    def _apply_merge(self, word_chars, pair, new_token):
        """Apply a merge rule to a word."""
        new_word = []
        i = 0
        while i < len(word_chars):
            if (i < len(word_chars) - 1 and 
                word_chars[i] == pair[0] and 
                word_chars[i + 1] == pair[1]):
                new_word.append(new_token)
                i += 2
            else:
                new_word.append(word_chars[i])
                i += 1
        return new_word

    def encode(self, text):
        """Encode text to token IDs with improved handling."""
        if not text.strip():
            return []
        
        text = self.clean_text(text)
        words = text.split()
        
        encoded = []
        for word in words:
            if not word.strip():
                continue

            word_tokens = list(word) + ['</w>']

            while True:
                pairs = self.get_pairs(word_tokens)
                if not pairs:
                    break
                
                best_pair = None
                for pair in pairs:
                    if pair in self.merges:
                        if best_pair is None or len(self.merges[pair]) > len(self.merges[best_pair]):
                            best_pair = pair
                
                if best_pair is None:
                    break
                
                word_tokens = self._apply_merge(word_tokens, best_pair, self.merges[best_pair])
            
            for token in word_tokens:
                token_id = self.vocab.get(token, self.vocab[self.unk_token])
                encoded.append(token_id)
        
        return encoded

    def decode(self, token_ids):
        """Decode token IDs back to text with improved handling."""
        if not token_ids:
            return ""
        
        tokens = []
        for token_id in token_ids:
            if token_id in self.inverse_vocab:
                token = self.inverse_vocab[token_id]
                if token not in [self.pad_token, self.bos_token, self.eos_token]:
                    tokens.append(token)
        
        text = ''.join(tokens)
        text = text.replace('</w>', ' ')
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text

print("Improved tokenizer ready!")

class MultiHeadAttention(nn.Module):
    def __init__(self, embed_dim, num_heads, dropout=0.1):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        
        self.q_linear = nn.Linear(embed_dim, embed_dim)
        self.k_linear = nn.Linear(embed_dim, embed_dim)
        self.v_linear = nn.Linear(embed_dim, embed_dim)
        self.out_linear = nn.Linear(embed_dim, embed_dim)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, query, key, value, mask=None):
        batch_size, seq_len = query.size(0), query.size(1)
        
        Q = self.q_linear(query).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        K = self.k_linear(key).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        V = self.v_linear(value).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.head_dim)
        
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        context = torch.matmul(attn_weights, V)
        context = context.transpose(1, 2).contiguous().view(batch_size, seq_len, self.embed_dim)
        
        return self.out_linear(context)

class FeedForward(nn.Module):
    def __init__(self, embed_dim, ff_dim, dropout=0.1):
        super().__init__()
        self.linear1 = nn.Linear(embed_dim, ff_dim)
        self.linear2 = nn.Linear(ff_dim, embed_dim)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        return self.linear2(self.dropout(F.relu(self.linear1(x))))

class TransformerBlock(nn.Module):
    def __init__(self, embed_dim, num_heads, ff_dim, dropout=0.1):
        super().__init__()
        self.attention = MultiHeadAttention(embed_dim, num_heads, dropout)
        self.feed_forward = FeedForward(embed_dim, ff_dim, dropout)
        self.norm1 = nn.LayerNorm(embed_dim)
        self.norm2 = nn.LayerNorm(embed_dim)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x, mask=None):
        attn_out = self.attention(x, x, x, mask)
        x = self.norm1(x + self.dropout(attn_out))
        ff_out = self.feed_forward(x)
        x = self.norm2(x + self.dropout(ff_out))
        return x

class LanguageModel(nn.Module):
    def __init__(self, vocab_size, embed_dim, num_layers, num_heads, ff_dim, max_seq_len, dropout=0.1):
        super().__init__()
        self.embed_dim = embed_dim
        self.token_embedding = nn.Embedding(vocab_size, embed_dim)
        self.pos_embedding = nn.Embedding(max_seq_len, embed_dim)
        
        self.blocks = nn.ModuleList([
            TransformerBlock(embed_dim, num_heads, ff_dim, dropout)
            for _ in range(num_layers)
        ])
        
        self.ln_f = nn.LayerNorm(embed_dim)
        self.head = nn.Linear(embed_dim, vocab_size, bias=False)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x, targets=None):
        batch_size, seq_len = x.size()
        
        # Create causal mask
        mask = torch.tril(torch.ones(seq_len, seq_len)).unsqueeze(0).unsqueeze(0).to(x.device)
        
        # Embeddings
        pos = torch.arange(0, seq_len, dtype=torch.long, device=x.device).unsqueeze(0)
        tok_emb = self.token_embedding(x)
        pos_emb = self.pos_embedding(pos)
        x = self.dropout(tok_emb + pos_emb)
        
        # Transformer blocks
        for block in self.blocks:
            x = block(x, mask)
        
        x = self.ln_f(x)
        logits = self.head(x)
        
        loss = None
        if targets is not None:
            loss = F.cross_entropy(logits.view(-1, logits.size(-1)), targets.view(-1), ignore_index=-1)
        
        return logits, loss

print("Model architecture ready!")


print("Loading dataset...")
dataset = load_dataset("wikitext", "wikitext-103-raw-v1", split="train[:100000]")
texts = [entry["text"].strip() for entry in dataset if entry["text"].strip()]
print(f"Loaded {len(texts)} text samples")

print("Training improved tokenizer...")
tokenizer = BPETokenizer()
tokenizer.train(texts[:20000], vocab_size=8000)  

print("Tokenizing texts...")
tokenized_texts = []
for i, text in enumerate(texts):
    if i % 2000 == 0:
        print(f"Tokenized {i}/{len(texts)} texts", end='\r')
    tokens = tokenizer.encode(text)
    if len(tokens) > 10:
        tokenized_texts.append(tokens)

print(f"\nTokenized {len(tokenized_texts)} texts")
print(f"Vocabulary size: {len(tokenizer.vocab)}")

class TextDataset(Dataset):
    def __init__(self, tokenized_texts, max_length=128):
        self.data = []
        for tokens in tokenized_texts:
            for i in range(0, len(tokens) - max_length, max_length // 4):
                chunk = tokens[i:i + max_length]
                if len(chunk) == max_length:
                    self.data.append(chunk)

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        tokens = self.data[idx]
        x = torch.tensor(tokens[:-1], dtype=torch.long)
        y = torch.tensor(tokens[1:], dtype=torch.long)
        return x, y

dataset = TextDataset(tokenized_texts, max_length=128)
dataloader = DataLoader(dataset, batch_size=16, shuffle=True, num_workers=2)

print(f"Dataset ready with {len(dataset)} samples")
print(f"Batches per epoch: {len(dataloader)}")

# ============================================================================
# MODEL CONFIGURATION AND TRAINING SETUP
# ============================================================================

# Improved model configuration
model_config = {
    'vocab_size': len(tokenizer.vocab),
    'embed_dim': 512,  # Increased embedding dimension
    'num_layers': 8,   # More layers
    'num_heads': 8,    # More attention heads
    'ff_dim': 2048,    # Larger feed-forward dimension
    'max_seq_len': 128,
    'dropout': 0.1
}

print(f"Model config: {model_config}")

# Initialize model
model = LanguageModel(**model_config).to(device)
optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4, weight_decay=0.01)  # Lower learning rate
scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=2000)

# Count parameters
total_params = sum(p.numel() for p in model.parameters())
print(f"Model has {total_params:,} parameters")

# Training function with better monitoring
def train_epoch(model, dataloader, optimizer, scheduler, device):
    model.train()
    total_loss = 0
    total_tokens = 0

    for batch_idx, (x, y) in enumerate(dataloader):
        x, y = x.to(device), y.to(device)

        optimizer.zero_grad()

        # Forward pass
        logits, loss = model(x, y)

        # Backward pass
        loss.backward()

        # Gradient clipping for stability
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)

        optimizer.step()
        scheduler.step()

        total_loss += loss.item()
        total_tokens += y.numel()

        if batch_idx % 200 == 0:
            current_loss = total_loss / (batch_idx + 1)
            perplexity = math.exp(min(current_loss, 10))  # Cap perplexity for display
            print(f"Batch {batch_idx:4d}/{len(dataloader)} | Loss: {current_loss:.4f} | PPL: {perplexity:.2f} | LR: {scheduler.get_last_lr()[0]:.6f}")

    avg_loss = total_loss / len(dataloader)
    perplexity = math.exp(min(avg_loss, 10))
    return avg_loss, perplexity

print("Training setup complete!")

# ============================================================================
# TRAINING LOOP
# ============================================================================

num_epochs = 20  # More epochs for better training
best_loss = float('inf')

print(f"Starting training for {num_epochs} epochs...")
print("=" * 60)

for epoch in range(num_epochs):
    start_time = time.time()

    avg_loss, perplexity = train_epoch(model, dataloader, optimizer, scheduler, device)

    epoch_time = time.time() - start_time

    print(f"\nEpoch {epoch+1:2d}/{num_epochs} | Loss: {avg_loss:.4f} | PPL: {perplexity:.2f} | Time: {epoch_time:.1f}s")
    print("=" * 60)

    # Save best model
    if avg_loss < best_loss:
        best_loss = avg_loss
        torch.save({
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'epoch': epoch,
            'loss': avg_loss,
            'config': model_config
        }, 'improved_model.pt')
        print(f"💾 Saved improved model (loss: {avg_loss:.4f})")

    # Save tokenizer
    with open('improved_tokenizer.pkl', 'wb') as f:
        pickle.dump(tokenizer, f)

print("\n🎉 Training completed!")
print(f"Best loss: {best_loss:.4f} | Best perplexity: {math.exp(best_loss):.2f}")

# ============================================================================
# TEXT GENERATION AND TESTING
# ============================================================================

def generate_text(model, tokenizer, prompt, max_length=50, temperature=0.8, top_k=40):
    model.eval()

    # Encode prompt
    tokens = tokenizer.encode(prompt)
    if not tokens:
        tokens = [tokenizer.vocab.get(tokenizer.bos_token, 0)]

    input_ids = torch.tensor([tokens], dtype=torch.long).to(device)
    generated = tokens.copy()

    with torch.no_grad():
        for _ in range(max_length):
            # Get model predictions
            logits, _ = model(input_ids)

            # Get logits for last token and apply temperature
            next_token_logits = logits[0, -1, :] / temperature

            # Apply top-k filtering
            if top_k > 0:
                indices_to_remove = next_token_logits < torch.topk(next_token_logits, top_k)[0][..., -1, None]
                next_token_logits[indices_to_remove] = -float('inf')

            # Sample next token
            probs = F.softmax(next_token_logits, dim=-1)
            next_token = torch.multinomial(probs, 1).item()

            # Add to sequence
            generated.append(next_token)
            input_ids = torch.cat([input_ids, torch.tensor([[next_token]], device=device)], dim=1)

            # Keep only recent context
            if input_ids.size(1) > 127:
                input_ids = input_ids[:, -127:]

    return tokenizer.decode(generated)

# Load best model for generation
checkpoint = torch.load('improved_model.pt', map_location=device)
model.load_state_dict(checkpoint['model_state_dict'])
print(f"Loaded improved model from epoch {checkpoint['epoch']+1}")

# Test generation with the problematic prompts
test_prompts = [
    "The future of artificial intelligence",
    "Once upon a time in a distant galaxy",
    "The most important thing in life is",
    "Technology has changed the world by",
    "In the year 2050, humans will"
]

print("\n" + "=" * 60)
print("🤖 TESTING IMPROVED MODEL")
print("=" * 60)

for prompt in test_prompts:
    print(f"\n📝 Prompt: '{prompt}'")
    print("-" * 40)

    for temp in [0.7, 1.0]:
        generated = generate_text(model, tokenizer, prompt, max_length=40, temperature=temp)
        print(f"🌡️  Temp {temp}: {generated}")

print("\n" + "=" * 60)
print("✅ Improved model testing complete!")
print("The model should now generate much better, more fluent English text!")
print("No more repetitive patterns or excessive <unk> tokens!")

# Save final files for download
print("\n📁 Files ready for download:")
print("- improved_model.pt (trained model)")
print("- improved_tokenizer.pkl (tokenizer)")
print("\nDownload these files and place them in your local project:")
print("- improved_model.pt -> checkpoints/phase1_english/colab_model.pt")
print("- improved_tokenizer.pkl -> data/vocab/colab_tokenizer.pkl")
