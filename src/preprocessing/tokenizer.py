import collections
import os
import json

class BPETokenizer:
    """
    A simple Byte Pair Encoding (BPE) tokenizer implementation from scratch.
    """
    def __init__(self):
        self.vocab = {}
        self.merges = {} # Stores (pair_tuple): new_token_string (e.g., ('a', 'b'): 'ab')
        self.inverse_vocab = {}
        self.unk_token = '<unk>'
        self.pad_token = '<pad>' # Initialize pad_token here

    def get_pairs(self, tokens):
        """
        Get all unique adjacent pairs from a list of tokens.
        """
        pairs = collections.defaultdict(int)
        for i in range(len(tokens) - 1):
            pairs[(tokens[i], tokens[i+1])] += 1
        return pairs

    def train(self, texts, vocab_size):
        """
        Trains the BPE tokenizer on a list of texts.
        """
        # 1. Initialize vocabulary with all unique characters in the input texts
        all_initial_tokens = set()
        # Add characters from all texts
        for text in texts:
            for char in text:
                all_initial_tokens.add(char)
        
        # Add special end-of-word token
        all_initial_tokens.add('</w>') 
        # Add unknown token
        all_initial_tokens.add(self.unk_token)
        # Add pad token
        all_initial_tokens.add(self.pad_token)
        
        sorted_initial_tokens = sorted(list(all_initial_tokens))
        
        self.vocab = {token: i for i, token in enumerate(sorted_initial_tokens)}
        self.inverse_vocab = {i: token for i, token in enumerate(sorted_initial_tokens)}
        current_id = len(sorted_initial_tokens)

        self.merges = {} # Stores (pair_tuple): new_token_string (e.g., ('a', 'b'): 'ab')

        # Prepare initial word segments for training
        # Each "word" (from splitting by space) becomes a sequence of its characters + </w>
        # e.g., "hello world" -> {"h e l l o </w>": count, "w o r l d </w>": count}
        word_segments_frequency = collections.defaultdict(int)
        for text in texts:
            # Simple word split by space. Consider more robust splitting later if needed.
            for word in text.split(' '):
                if word: # Ensure not empty string from multiple spaces
                    # Represent word as space-separated characters + '</w>' for BPE processing
                    char_segment = ' '.join(list(word)) + ' </w>'
                    word_segments_frequency[char_segment] += 1
        
        # `current_segments`: a dictionary mapping space-separated token strings (representing words) to their counts.
        current_segments = dict(word_segments_frequency) # Make a copy

        while len(self.vocab) < vocab_size:
            pairs_frequency = collections.defaultdict(int)
            for segment_str, count in current_segments.items():
                tokens_in_segment = segment_str.split(' ')
                current_pairs = self.get_pairs(tokens_in_segment)
                for pair, freq in current_pairs.items():
                    pairs_frequency[pair] += freq * count # Accumulate frequency weighted by segment count

            if not pairs_frequency:
                break # No more pairs to merge

            # Find the most frequent pair
            # Fixing linter error by explicitly getting items and using lambda
            best_pair = max(pairs_frequency.items(), key=lambda item: item[1])[0] 
            
            first_token, second_token = best_pair
            new_merged_token_string = first_token + second_token

            # Store the merge rule
            self.merges[best_pair] = new_merged_token_string

            # Update `current_segments` by applying the merge
            updated_segments = collections.defaultdict(int)
            for segment_str, count in current_segments.items():
                tokens_in_segment = segment_str.split(' ')
                
                # Replace occurrences of the best_pair in the segment's tokens
                # This needs to be done carefully to avoid unintended replacements.
                # Use a string replace on the space-separated string, but ensure it doesn't break existing tokens.
                # Example: "a b c" replace "a b" with "ab" -> "ab c"
                
                # Directly rebuild the token list for robustness
                i = 0
                new_tokens_for_segment = []
                while i < len(tokens_in_segment):
                    if i < len(tokens_in_segment) - 1 and \
                       tokens_in_segment[i] == first_token and \
                       tokens_in_segment[i+1] == second_token:
                        new_tokens_for_segment.append(new_merged_token_string)
                        i += 2
                    else:
                        new_tokens_for_segment.append(tokens_in_segment[i])
                        i += 1
                updated_segments[' '.join(new_tokens_for_segment)] += count
            current_segments = updated_segments

            # Add the new merged token to the vocabulary
            if new_merged_token_string not in self.vocab:
                self.vocab[new_merged_token_string] = current_id
                self.inverse_vocab[current_id] = new_merged_token_string
                current_id += 1

        print(f"BPE tokenizer trained with vocabulary size: {len(self.vocab)}")


    def encode(self, text):
        """
        Encodes a text into BPE tokens.
        """
        if not isinstance(text, str):
            raise TypeError("Input text must be a string.")

        # Split text into words. Each word will be processed independently for BPE subword tokenization.
        # Spaces between words will effectively act as delimiters for BPE, not mergeable units themselves.
        
        words_in_text = text.split(' ')
        encoded_token_ids = []

        for i, word in enumerate(words_in_text):
            if not word: # Handle empty strings from multiple spaces, representing a space token
                if ' ' in self.vocab: # If space is in our base vocab, add it.
                    encoded_token_ids.append(self.vocab[' '])
                continue # Skip to next word

            # Prepare word for encoding: split into characters and add end-of-word token
            tokens_for_word_processing = list(word) + ['</w>'] 
            
            # Iteratively apply merges to the current list of tokens for this word
            while True:
                # Get all possible adjacent pairs in the current word's token list
                current_pairs_in_word = self.get_pairs(tokens_for_word_processing)

                potential_merges = []
                for p in current_pairs_in_word.keys():
                    if p in self.merges:
                        potential_merges.append((p, self.merges[p]))
                
                if not potential_merges:
                    break # No more mergeable pairs for this word

                # Select the best pair to merge. Heuristic: prioritize by length of merged token.
                # This helps ensure longer, more meaningful subwords are formed.
                # If lengths are equal, sort by pair elements for determinism.
                selected_pair_and_merged_token = max(potential_merges, 
                                                     key=lambda x: (len(x[1]), x[0])) # x[0] is the pair tuple itself
                
                selected_pair = selected_pair_and_merged_token[0] # The (first, second) tuple
                new_token_str = selected_pair_and_merged_token[1] # The merged string (e.g., 'he')

                first, second = selected_pair

                # Apply the merge by rebuilding the list of tokens for the current word
                j = 0
                new_tokens_for_word_processing = []
                while j < len(tokens_for_word_processing):
                    if j < len(tokens_for_word_processing) - 1 and \
                       tokens_for_word_processing[j] == first and \
                       tokens_for_word_processing[j+1] == second:
                        new_tokens_for_word_processing.append(new_token_str)
                        j += 2
                    else:
                        new_tokens_for_word_processing.append(tokens_for_word_processing[j])
                        j += 1
                tokens_for_word_processing = new_tokens_for_word_processing # Update the tokens for the current word

            # After all merges for a word are applied, convert to token IDs
            for token in tokens_for_word_processing:
                if token in self.vocab:
                    encoded_token_ids.append(self.vocab[token])
                else:
                    # Fallback for OOV (Out-Of-Vocabulary) tokens or characters not in initial vocab.
                    # Assign UNK token ID for any token not found in the vocabulary.
                    encoded_token_ids.append(self.vocab[self.unk_token])
            
            # Add a space token after each word, except the last one.
            # This ensures reconstruction preserves spaces between words.
            if i < len(words_in_text) - 1 and ' ' in self.vocab:
                encoded_token_ids.append(self.vocab[' '])

        return encoded_token_ids

    def decode(self, token_ids):
        """
        Decodes a list of token IDs back into text.
        """
        decoded_parts = []
        for token_id in token_ids:
            decoded_token = self.inverse_vocab.get(token_id, '') 
            decoded_parts.append(decoded_token)
        
        # Join all decoded parts.
        # Then, handle the special tokens like '</w>' and ' ' to reconstruct the original text.
        
        raw_decoded_string = "".join(decoded_parts)
        
        # Replace '</w>' with an empty string, then remove the trailing space if it was just '</w> '
        text_without_end_markers = raw_decoded_string.replace('</w>', '')
        
        final_text = text_without_end_markers.strip()
        # Further clean up any multiple spaces resulting from the replacement, if necessary.
        final_text = ' '.join(final_text.split())

        return final_text

    def save_tokenizer(self, vocab_path="data/vocab/tokenizer_model.json", merges_path="data/vocab/merges.json"):
        """
        Saves the tokenizer vocabulary and merges to JSON files.
        """
        os.makedirs(os.path.dirname(vocab_path), exist_ok=True)
        
        with open(vocab_path, "w", encoding="utf-8") as f:
            json.dump(self.vocab, f, ensure_ascii=False, indent=4)
        print(f"Tokenizer vocabulary saved to {vocab_path}")

        # Save merges: Convert tuple keys to strings for JSON serialization
        serializable_merges = {f"{p[0]} {p[1]}": merged_token for p, merged_token in self.merges.items()}
        with open(merges_path, "w", encoding="utf-8") as f:
            json.dump(serializable_merges, f, ensure_ascii=False, indent=4)
        print(f"Tokenizer merges saved to {merges_path}")

    @classmethod
    def load_tokenizer(cls, vocab_path="data/vocab/tokenizer_model.json", merges_path="data/vocab/merges.json"):
        """
        Loads the tokenizer vocabulary and merges from JSON files.
        """
        tokenizer = cls()
        with open(vocab_path, "r", encoding="utf-8") as f:
            tokenizer.vocab = json.load(f)
        
        # Reconstruct inverse_vocab
        tokenizer.inverse_vocab = {v: k for k, v in tokenizer.vocab.items()}

        try:
            with open(merges_path, "r", encoding="utf-8") as f:
                serializable_merges = json.load(f)
                # Convert string keys back to tuple keys
                tokenizer.merges = {tuple(k.split(' ')): v for k, v in serializable_merges.items()}
        except FileNotFoundError:
            print(f"Warning: Merges file not found at {merges_path}. Tokenizer will operate without merges.")
            tokenizer.merges = {}
        except json.JSONDecodeError:
            print(f"Warning: Error decoding merges from {merges_path}. Tokenizer will operate without merges.")
            tokenizer.merges = {}
        
        print(f"Tokenizer loaded from {vocab_path} and {merges_path}")
        return tokenizer

# Example usage (for testing purposes, not part of the main workflow)
if __name__ == "__main__":
    texts = [
        "hello world",
        "world peace",
        "apple pie",
        "apples are delicious",
        "apple",
        "apples",
        "This is a test sentence.", # Added a more complex sentence
        "programming language",
        "data science"
    ]
    
    tokenizer = BPETokenizer()
    # Increased vocab_size for more merges
    tokenizer.train(texts, vocab_size=300) 

    test_text_1 = "hello apple delicious peace"
    encoded_1 = tokenizer.encode(test_text_1)
    print(f"Encoded '{test_text_1}': {encoded_1}")
    print(f"Decoded '{test_text_1}': {tokenizer.decode(encoded_1)}")

    test_text_2 = "This is a programming language for data science."
    encoded_2 = tokenizer.encode(test_text_2)
    print(f"Encoded '{test_text_2}': {encoded_2}")
    print(f"Decoded '{test_text_2}': {tokenizer.decode(encoded_2)}")

    tokenizer.save_tokenizer()
    loaded_tokenizer = BPETokenizer.load_tokenizer()
    encoded_loaded_1 = loaded_tokenizer.encode(test_text_1)
    print(f"Encoded (loaded) '{test_text_1}': {encoded_loaded_1}")
    print(f"Decoded (loaded) '{test_text_1}': {loaded_tokenizer.decode(encoded_loaded_1)}")
    
    encoded_loaded_2 = loaded_tokenizer.encode(test_text_2)
    print(f"Encoded (loaded) '{test_text_2}': {encoded_loaded_2}")
    print(f"Decoded (loaded) '{test_text_2}': {loaded_tokenizer.decode(encoded_loaded_2)}")

    # Test with OOV character handling
    oov_text_char = "new£word"
    encoded_oov_char = tokenizer.encode(oov_text_char)
    print(f"Encoded '{oov_text_char}': {encoded_oov_char}")
    print(f"Decoded '{oov_text_char}': {tokenizer.decode(encoded_oov_char)}")

    # Test with OOV word handling
    oov_word_text = "This is an unknownword for testing."
    encoded_oov_word = tokenizer.encode(oov_word_text)
    print(f"Encoded '{oov_word_text}': {encoded_oov_word}")
    print(f"Decoded '{oov_word_text}': {tokenizer.decode(encoded_oov_word)}")

    # Test with invalid input
    try:
        tokenizer.encode(123)
    except TypeError as e:
        print(f"Caught expected error: {e}")

    # Test loading non-existent tokenizer
    print("\nTesting loading non-existent tokenizer:")
    loaded_tokenizer_fail = BPETokenizer.load_tokenizer(vocab_path="non_existent_vocab.json", merges_path="non_existent_merges.json")
    encoded_fail = loaded_tokenizer_fail.encode("hello world")
    print(f"Encoded with failed load: {encoded_fail}") 